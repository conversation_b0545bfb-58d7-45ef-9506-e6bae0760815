<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
  } from '$lib/components/ui/table';
  import type { MarketEvent } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';

  // 本地实现辅助函数
  function getEventTypeLabel(eventType: string): string {
    return MARKET_EVENT_TYPE_LABELS[eventType as keyof typeof MARKET_EVENT_TYPE_LABELS] || eventType;
  }

  function getParameterValue(event: MarketEvent, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }

  function formatEventDate(timestamp: number): string {
    return new Date(timestamp * 1000).toLocaleString('zh-CN');
  }

  interface Props {
    results: MarketEvent[];
  }

  const { results = [] }: Props = $props();

  // 获取事件的显示名称
  function getEventDisplayName(event: MarketEvent): string {
    const base = getParameterValue(event, 'base') as string;
    const pair = getParameterValue(event, 'pair') as string;
    const exchangeLabel = getParameterValue(event, 'exchangeLabel') as string;

    if (base) {
      return `${base} - ${getEventTypeLabel(event.marketEventType)}`;
    } else if (pair) {
      return `${pair} - ${getEventTypeLabel(event.marketEventType)}`;
    } else if (exchangeLabel) {
      return `${exchangeLabel} - ${getEventTypeLabel(event.marketEventType)}`;
    }

    return getEventTypeLabel(event.marketEventType);
  }

  // 获取事件类型对应的 Badge 变体
  function getEventTypeVariant(eventType: string): 'default' | 'secondary' | 'destructive' | 'outline' {
    switch (eventType) {
      case 'LIQUIDATION_ORDER':
        return 'destructive';
      case 'TWAP_DETECTION':
        return 'default';
      case 'VOLUME_INCREASE':
        return 'secondary';
      default:
        return 'outline';
    }
  }
</script>

<!-- 统一的响应式表格 -->
<div class="w-full">
  <div class="rounded-md border">
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead class="w-[200px]">ID</TableHead>
          <TableHead>事件名称</TableHead>
          <TableHead class="hidden lg:table-cell">事件类型</TableHead>
          <TableHead class="hidden md:table-cell">金额 (USD)</TableHead>
          <TableHead class="hidden xl:table-cell">事件时间</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {#each results as item (item.id)}
          <TableRow>
            <TableCell class="font-mono text-xs">{item.id}</TableCell>
            <TableCell class="font-medium">{getEventDisplayName(item)}</TableCell>
            <TableCell class="hidden lg:table-cell">
              <Badge variant={getEventTypeVariant(item.marketEventType)} class="text-xs">
                {getEventTypeLabel(item.marketEventType)}
              </Badge>
            </TableCell>
            <TableCell class="hidden md:table-cell">
              {#if getParameterValue(item, 'amountUsd')}
                <span class="font-mono">
                  ${Number(getParameterValue(item, 'amountUsd')).toLocaleString()}
                </span>
              {:else}
                <span class="text-muted-foreground">-</span>
              {/if}
            </TableCell>
            <TableCell class="text-muted-foreground hidden xl:table-cell">
              {formatEventDate(item.date)}
            </TableCell>
          </TableRow>
        {:else}
          <TableRow>
            <TableCell colspan={5} class="h-24 text-center">没有找到匹配的数据。</TableCell>
          </TableRow>
        {/each}
      </TableBody>
    </Table>
  </div>
</div>

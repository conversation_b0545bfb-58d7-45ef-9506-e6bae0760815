<!-- src/lib/components/layout/AppSidebar/AppSidebar.svelte -->
<script lang="ts">
  import { SystemStatus } from '$lib/components/features/system';
  // 导入现有的功能组件
  import { ThemeToggle } from '$lib/components/features/theme';
  import { Separator } from '$lib/components/ui/separator/index.js';
  // shadcn-svelte 组件导入
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';

  import { sidebarConfig } from './config.js';
  // 导入子组件
  import NavMain from './NavMain.svelte';
  import NavProjects from './NavProjects.svelte';
  import NavUser from './NavUser.svelte';
  import TeamSwitcher from './TeamSwitcher.svelte';
  import type { SidebarConfig } from './types.js';

  interface Props {
    config?: SidebarConfig;
  }

  const { config = sidebarConfig }: Props = $props();
</script>

<Sidebar.Root collapsible="icon" class="group-data-[collapsible=icon]:w-12">
  <!-- 侧边栏头部 -->
  <Sidebar.Header>
    {#if config.teams && config.teams.length > 0}
      <TeamSwitcher teams={config.teams} />
    {:else}
      <!-- 默认 Logo 区域 -->
      <div class="flex h-12 items-center gap-2 px-4">
        <div
          class="bg-sidebar-primary/10 border-sidebar-primary/20 flex h-8 w-8 items-center justify-center rounded-lg border"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="text-sidebar-primary h-5 w-5 flex-shrink-0"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            stroke-width="2"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M14 10l-2 1m0 0l-2-1m2 1v2.5M20 7l-2 1m-2-2l-2 1M4 7l2 1m2-2l2 1m2 10l2-1m-2 1v-2.5M10 17l-2-1m2 1v-2.5M20 17l-2-1m-2 2l-2-1M4 17l2-1m2 2l2-1"
            />
          </svg>
        </div>
        <span
          class="text-sidebar-foreground text-lg font-bold group-data-[collapsible=icon]:hidden"
        >
          数据监控
        </span>
      </div>
    {/if}
  </Sidebar.Header>

  <!-- 侧边栏内容 -->
  <Sidebar.Content>
    <!-- 主导航 -->
    <NavMain items={config.navMain} />

    <!-- 项目快捷访问 -->
    {#if config.projects && config.projects.length > 0}
      <NavProjects projects={config.projects} />
    {/if}
  </Sidebar.Content>

  <!-- 侧边栏底部 -->
  <Sidebar.Footer>
    <!-- 系统状态（仅在展开状态显示） -->
    {#if config.showSystemStatus}
      <div class="px-3 py-2 group-data-[collapsible=icon]:hidden">
        <SystemStatus />
      </div>
      <Separator class="group-data-[collapsible=icon]:hidden" />
    {/if}

    <!-- 展开状态下的布局：水平排列 -->
    <div class="flex items-center gap-2 p-2 group-data-[collapsible=icon]:hidden">
      <!-- 主题切换器 -->
      {#if config.showThemeToggle}
        <div class="flex-shrink-0">
          <ThemeToggle />
        </div>
      {/if}

      <!-- 用户信息 -->
      <div class="min-w-0 flex-1">
        <NavUser user={config.user} />
      </div>
    </div>

    <!-- 收起状态下的布局：垂直排列 -->
    <div
      class="hidden group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:items-center group-data-[collapsible=icon]:gap-2 group-data-[collapsible=icon]:p-2"
    >
      <!-- 主题切换器 -->
      {#if config.showThemeToggle}
        <div class="flex-shrink-0">
          <ThemeToggle />
        </div>
      {/if}

      <!-- 用户信息 -->
      <div class="flex justify-center">
        <NavUser user={config.user} />
      </div>
    </div>
  </Sidebar.Footer>

  <!-- 侧边栏导轨（用于拖拽调整） -->
  <Sidebar.Rail />
</Sidebar.Root>

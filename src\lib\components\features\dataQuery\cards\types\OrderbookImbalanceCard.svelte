<script lang="ts">
  import { Badge } from '$lib/components/ui/badge';
  import { Card, CardContent } from '$lib/components/ui/card';
  // 本地实现 getParameterValue 函数
  function getParameterValue(event: any, parameterId: string): string | number | boolean | undefined {
    const parameter = event.parameters.find((p: any) => p.parameterId === parameterId);
    return parameter?.value;
  }
  import {
    formatUsdAmount,
    type BaseEventCardProps
  } from '../utils';

  interface Props extends BaseEventCardProps {}

  const {
    event,
    isHighlighted = false,
    onCardClick
  }: Props = $props();

  // 提取订单簿失衡相关参数 - 只保留必要的基础信息
  const deltaUsd = getParameterValue(event, 'deltaUsd') as number;
  const side = getParameterValue(event, 'side') as number;
  const exchangeLabelTopBidder = getParameterValue(event, 'exchangeLabelTopBidder') as string;
  const exchangeLabelTopAsker = getParameterValue(event, 'exchangeLabelTopAsker') as string;
  const base = getParameterValue(event, 'base') as string;

  // 获取失衡方向
  const imbalanceDirection = side === 1 ? '买盘优势' : side === 2 ? '卖盘优势' : '未知';
  const imbalanceBadgeVariant = side === 1 ? 'default' : 'destructive';

  // 获取主要交易所（优势方）
  const mainExchange = side === 1 ? exchangeLabelTopBidder : exchangeLabelTopAsker;

  // 卡片样式
  const cardClasses = `
    transition-all duration-200 w-full
    ${isHighlighted
      ? 'ring-primary border-primary/50 shadow-lg ring-2'
      : 'hover:shadow-md'
    }
    ${onCardClick ? 'cursor-pointer' : ''}
  `;

  // 处理卡片点击
  function handleCardClick() {
    onCardClick?.(event);
  }
</script>

<Card
  class={cardClasses}
  onclick={onCardClick ? handleCardClick : undefined}
  role={onCardClick ? 'button' : undefined}
  tabindex={onCardClick ? 0 : undefined}
>
  <!-- 简化的卡片内容 -->
  <CardContent class="p-3">
    <!-- 主要信息行 -->
    <div class="flex items-center justify-between mb-2">
      <!-- 左侧：币种 -->
      <div class="flex items-center space-x-2 min-w-0 flex-1">
        <!-- 币种 -->
        <span class="text-foreground font-semibold text-sm">
          {base || '未知'}
        </span>

        <!-- 订单簿标识 -->
        <span class="text-muted-foreground text-xs">
          订单簿
        </span>
      </div>

      <!-- 右侧：失衡方向 -->
      <Badge variant={imbalanceBadgeVariant} class="text-xs flex-shrink-0">
        {imbalanceDirection}
      </Badge>
    </div>

    <!-- 交易所和关键数据 -->
    <div class="grid grid-cols-2 gap-2 text-xs">
      <!-- 主要交易所 -->
      <div class="space-y-1">
        <div class="text-muted-foreground">主要交易所</div>
        <div class="text-foreground font-medium truncate" title={mainExchange}>
          {mainExchange || '未知'}
        </div>
      </div>

      <!-- 失衡金额（关键数据） -->
      <div class="space-y-1">
        <div class="text-muted-foreground">失衡金额</div>
        <div class="text-foreground font-semibold">
          {formatUsdAmount(Math.abs(deltaUsd))}
        </div>
      </div>
    </div>
  </CardContent>
</Card>

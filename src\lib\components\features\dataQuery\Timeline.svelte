<script lang="ts">
  import { Separator } from '$lib/components/ui/separator';
  import type { MarketEvent, MarketEventType } from '$lib/types';
  import { MARKET_EVENT_TYPE_LABELS } from '$lib/types';
  import { EventCardFactory } from './cards';
  import { cardInteractionStore } from '$lib/stores/features/cardInteraction';
  import EventCardDetailModal from './cards/EventCardDetailModal.svelte';

  interface Props {
    items: MarketEvent[];
  }

  const { items = [] }: Props = $props();

  // 获取所有唯一的市场事件类型
  const uniqueTypes: MarketEventType[] = $derived([...new Set(items.map((item) => item.marketEventType))].sort() as MarketEventType[]);

  // 处理卡片点击事件
  function handleCardClick(event: MarketEvent) {
    cardInteractionStore.showDetailModal(event);
  }

  // 处理模态框关闭
  function handleModalClose() {
    cardInteractionStore.hideDetailModal();
  }

  // 将Unix时间戳归并到15分钟间隔
  function roundToFifteenMinutes(timestamp: number): string {
    const date = new Date(timestamp * 1000); // 转换为毫秒
    const minutes = date.getMinutes();
    const roundedMinutes = Math.floor(minutes / 15) * 15;

    const roundedDate = new Date(date);
    roundedDate.setMinutes(roundedMinutes, 0, 0); // 设置分钟、秒、毫秒

    return roundedDate.toISOString();
  }

  // 按15分钟间隔分组数据
  const itemsByTimeSlot: Record<string, MarketEvent[]> = $derived(
    items.reduce(
      (acc, item) => {
        const timeSlot = roundToFifteenMinutes(item.date);
        if (!acc[timeSlot]) {
          acc[timeSlot] = [];
        }
        acc[timeSlot].push(item);
        return acc;
      },
      {} as Record<string, MarketEvent[]>
    )
  );

  // 获取所有15分钟时间间隔并排序
  const allTimeSlots: string[] = $derived(
    Object.keys(itemsByTimeSlot).sort((a, b) => new Date(b).getTime() - new Date(a).getTime())
  );

  // 格式化时间显示（显示15分钟时间范围）
  function formatTime(dateString: string): string {
    const date = new Date(dateString);
    const endDate = new Date(date.getTime() + 15 * 60 * 1000); // 加15分钟

    const startTime = date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    const endTime = endDate.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
    });

    return `${startTime}-${endTime}`;
  }

  // 格式化日期显示
  function formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
    });
  }


</script>

<div class="relative w-full" style="z-index: 1;">
  {#if uniqueTypes.length === 0}
    <!-- 空状态 -->
    <div class="flex flex-col items-center justify-center py-12 text-center">
      <div class="bg-muted mb-4 flex h-12 w-12 items-center justify-center rounded-full">
        <svg
          class="text-muted-foreground h-6 w-6"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
          ></path>
        </svg>
      </div>
      <p class="text-muted-foreground text-sm">暂无时间轴数据</p>
    </div>
  {:else}
    <!-- 表头：类型列标题 -->
    <div class="mb-6 flex">
      <div class="w-24 flex-shrink-0"></div>
      <!-- 时间列占位 -->
      <div class="w-1 flex-shrink-0"></div>
      <!-- 时间轴占位 -->
      <div
        class="grid flex-1 gap-3 pt-2 pl-4"
        style="grid-template-columns: repeat({uniqueTypes.length}, 1fr);"
      >
        {#each uniqueTypes as type}
          <div class="text-center">
            <h3
              class="text-foreground bg-accent/30 border-border/20 rounded-lg border px-3 py-2 text-sm font-semibold"
            >
              {MARKET_EVENT_TYPE_LABELS[type as keyof typeof MARKET_EVENT_TYPE_LABELS] || type}
            </h3>
          </div>
        {/each}
      </div>
    </div>

    <!-- 时间轴主线 -->
    <Separator
      orientation="vertical"
      class="bg-border/60 dark:bg-border/40 absolute top-16 bottom-0 left-[6.125rem]"
    />

    <!-- 时间行 -->
    {#each allTimeSlots as timeSlot}
      {@const timeSlotItems = itemsByTimeSlot[timeSlot] || []}
      {@const totalItemsInSlot = timeSlotItems.length}
      <div class="relative mb-6 flex" class:mb-8={totalItemsInSlot > 3}>
        <!-- 时间显示（左侧） -->
        <div class="flex w-24 flex-shrink-0 flex-col justify-start pr-4 text-right">
          <div
            class="text-foreground bg-accent/20 mb-1 rounded px-2 py-1 font-mono text-xs font-medium"
          >
            {formatTime(timeSlot)}
          </div>
          <div class="text-muted-foreground mb-1 font-mono text-xs">
            {formatDate(timeSlot)}
          </div>
          <!-- 显示该时间段的总记录数 -->
          {#if totalItemsInSlot > 0}
            <div class="text-muted-foreground text-xs">
              {totalItemsInSlot} 条记录
            </div>
          {/if}
        </div>

        <!-- 时间轴节点 -->
        <div class="flex w-1 flex-shrink-0 items-start justify-center pt-2">
          <div
            class="bg-primary border-background dark:border-card flex size-4 items-center justify-center rounded-full border-2 shadow-sm"
            class:bg-primary={totalItemsInSlot > 0}
            class:bg-muted={totalItemsInSlot === 0}
          ></div>
        </div>

        <!-- 类型列 -->
        <div
          class="grid flex-1 items-start gap-3 pt-2 pl-4"
          style="grid-template-columns: repeat({uniqueTypes.length}, 1fr);"
        >
          {#each uniqueTypes as type}
            {@const typeItems = (itemsByTimeSlot[timeSlot] || []).filter(
              (item) => item.marketEventType === type
            )}
            <div class="flex min-h-[60px] flex-col space-y-2">
              {#if typeItems.length > 0}
                {#each typeItems as item (item.id)}
                  <EventCardFactory
                    event={item}
                    size="sm"
                    variant="compact"
                    useSpecificCard={true}
                    isHighlighted={$cardInteractionStore.selectedEventId === item.id}
                    onCardClick={handleCardClick}
                  />
                {/each}
                <!-- 显示该类型在此时间段的数据条数 -->
                {#if typeItems.length > 1}
                  <div class="text-muted-foreground py-1 text-center text-xs">
                    共 {typeItems.length} 条记录
                  </div>
                {/if}
              {/if}
            </div>
          {/each}
        </div>
      </div>
    {/each}
  {/if}
</div>

<!-- 详细卡片模态框 -->
<EventCardDetailModal
  open={$cardInteractionStore.showDetailModal}
  event={$cardInteractionStore.selectedEvent}
  onClose={handleModalClose}
/>

// src/lib/services/api/coinSearch.ts
import type { CoinSearchResponse } from '$lib/types';

import { BaseApiService } from './base';

/**
 * 币种搜索 API 服务
 */
export class CoinSearchApiService extends BaseApiService {
  constructor() {
    // 使用外部 API 基础 URL
    super('https://api.coinact.gg');
  }

  /**
   * 根据名称搜索币种
   * @param name 币种名称关键词
   * @param limit 返回结果数量限制，默认为 10
   * @returns 币种搜索结果
   */
  async searchCoins(name: string, limit: number = 10): Promise<CoinSearchResponse> {
    const endpoint = `/ranking/coins?orderBy=marketCap&desc=true&limit=${limit}&name=${encodeURIComponent(name)}`;

    const response = await this.get<CoinSearchResponse>(endpoint, {
      headers: {
        accept: '*/*',
        'content-type': 'application/json',
      },
      // 外部 API 可能较慢，增加超时时间
      timeout: 15000,
      // 减少重试次数，避免过多请求
      retry: {
        maxRetries: 1,
        retryDelay: 500,
      },
    });

    return response.data;
  }

  /**
   * 获取热门币种列表（无搜索关键词时使用）
   * @param limit 返回结果数量限制，默认为 10
   * @returns 热门币种列表
   */
  async getPopularCoins(limit: number = 10): Promise<CoinSearchResponse> {
    const endpoint = `/ranking/coins?orderBy=marketCap&desc=true&limit=${limit}`;

    const response = await this.get<CoinSearchResponse>(endpoint, {
      headers: {
        accept: '*/*',
        'content-type': 'application/json',
      },
      timeout: 15000,
      retry: {
        maxRetries: 1,
        retryDelay: 500,
      },
    });

    return response.data;
  }
}

// 创建并导出服务实例
export const coinSearchApiService = new CoinSearchApiService();
